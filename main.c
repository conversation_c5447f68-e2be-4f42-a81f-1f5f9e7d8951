#include <stdio.h>
#include "calculator.h"

int main() {
    int choice;
    int student_id;
    double num1, num2, result;
    char continue_choice;
    
    do {
        // 显示菜单
        display_menu();
        
        // 获取用户选择
        printf("请输入您的选择 (1-4): ");
        scanf("%d", &choice);
        
        if (choice < 1 || choice > 4) {
            printf("无效选择，请重新输入！\n");
            continue;
        }
        
        // 输入学号
        printf("请输入您的学号: ");
        scanf("%d", &student_id);
        
        // 输入两个数字
        printf("请输入两个数字，用空格分隔: ");
        scanf("%lf %lf", &num1, &num2);
        
        // 执行计算
        result = perform_calculation(choice, num1, num2);
        
        // 显示结果
        display_result(student_id, choice, num1, num2, result);
        
        // 询问是否继续
        printf("是否继续计算? (y/n): ");
        scanf(" %c", &continue_choice);
        
        printf("========== 计算结束 ==========\n\n");
        
    } while (continue_choice == 'y' || continue_choice == 'Y');
    
    printf("感谢使用计算器，再见！\n");
    
    return 0;
}
