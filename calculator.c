#include <stdio.h>
#include "calculator.h"

// 显示菜单
void display_menu(void) {
    printf("• student@debian:~/text of build\n");
    printf("• student@debian:~/text/build$ ./calc\n");
    printf("========== 计算器 ==========\n");
    printf("1. 加法\n");
    printf("2. 减法\n");
    printf("3. 乘法\n");
    printf("4. 除法\n");
    printf("\n");
}

// 执行计算
double perform_calculation(int choice, double num1, double num2) {
    switch (choice) {
        case 1:
            return num1 + num2;
        case 2:
            return num1 - num2;
        case 3:
            return num1 * num2;
        case 4:
            if (num2 != 0) {
                return num1 / num2;
            } else {
                printf("错误：除数不能为零！\n");
                return 0;
            }
        default:
            return 0;
    }
}

// 获取操作名称
const char* get_operation_name(int choice) {
    switch (choice) {
        case 1: return "加法";
        case 2: return "减法";
        case 3: return "乘法";
        case 4: return "除法";
        default: return "未知";
    }
}

// 显示结果
void display_result(int student_id, int choice, double num1, double num2, double result) {
    printf("请输入您的学号 (1-4): %d\n", choice);
    printf("请输入两个数字，用空格分隔: %.2f %.2f\n", num1, num2);
    printf("%.2f %c %.2f = %.2f\n", num1,
           (choice == 1) ? '+' : (choice == 2) ? '-' : (choice == 3) ? '*' : '/',
           num2, result);
}
